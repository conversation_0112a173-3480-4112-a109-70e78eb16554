.page {
  position: relative;
  width: 1920px;
  height: 4076px;
  overflow: hidden;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 1918px;
  height: 4076px;
  margin-left: 2px;
}

.section_1 {
  background-color: rgba(0, 0, 0, 1);
  position: relative;
  width: 1920px;
  height: 520px;
  overflow: hidden;
}

.block_1 {
  height: 1051px;
  background: url(./img/SketchPngc22cd3bfbdf2b4c82b16fe88718024391d5b03af75c1b89a6da888553145132a.png)
    0px 250px no-repeat;
  background-size: 1577px 520px;
  width: 1576px;
  position: absolute;
  left: 172px;
  top: -250px;
}

.section_6 {
  width: 172px;
  height: 44px;
  margin: 271px 0 0 294px;
}

.group_1 {
  border-radius: 50%;
  background-image: url(./img/6002fce2fbf946d3849ae479605d68ed_mergeImage.png);
  width: 42px;
  height: 42px;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 2px;
}

.text_1 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text-wrapper_11 {
  width: 192px;
  height: 48px;
  margin: 246px 0 0 694px;
}

.text_2 {
  width: 192px;
  height: 48px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 48px;
  font-family: SourceHanSansCN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 72px;
}

.text-wrapper_12 {
  width: 237px;
  height: 33px;
  margin: 7px 0 402px 670px;
}

.text_3 {
  width: 237px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.block_2 {
  background-color: rgba(9, 9, 9, 1);
  position: absolute;
  left: 442px;
  top: 92px;
  width: 1478px;
  height: 92px;
}

.text_4 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 50px;
}

.text_5 {
  width: 54px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 64px;
}

.text_6 {
  width: 36px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 64px;
}

.text-wrapper_3 {
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 122px;
  margin: 0 447px 0 570px;
}

.text_7 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 31px 0 0 26px;
}

.block_3 {
  background-color: rgba(255, 255, 255, 1);
  position: absolute;
  left: 679px;
  top: 0;
  width: 1240px;
  height: 92px;
}

.text_8 {
  width: 69px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 35px 0 0 108px;
}

.image_1 {
  width: 62px;
  height: 1px;
  margin: 46px 0 0 8px;
}

.text-wrapper_4 {
  background-color: rgba(14, 107, 228, 1);
  height: 92px;
  margin-left: -1px;
  width: 103px;
}

.text_9 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 20px;
}

.text_10 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 19px;
}

.text_11 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_12 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_13 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.thumbnail_1 {
  width: 14px;
  height: 14px;
  margin: 39px 446px 0 40px;
}

.group_11 {
  position: relative;
  width: 1918px;
  height: 3557px;
  margin-bottom: 1px;
}

.text_14 {
  width: 871px;
  height: 99px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 33px;
  margin: 48px 0 0 523px;
}

.text_15 {
  width: 142px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 50px;
  margin: 43px 0 0 889px;
}

.text_16 {
  width: 178px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 50px;
  margin: 1152px 0 0 871px;
}

.text_17 {
  width: 540px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  line-height: 22px;
  margin: 11px 0 0 698px;
}

.section_7 {
  width: 1166px;
  height: 415px;
  margin: 32px 0 0 377px;
}

.box_2 {
  box-shadow: 0px 0px 8px 0px rgba(86, 86, 86, 0.5);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 380px;
  height: 415px;
}

.image-text_5 {
  width: 340px;
  height: 358px;
  margin: 16px 0 0 20px;
}

.section_4 {
  background-image: url(./img/a7c1fa97577b41ac993c28c674e7202a_mergeImage.png);
  width: 340px;
  height: 180px;
}

.text-group_6 {
  width: 340px;
  height: 160px;
  margin-top: 18px;
}

.text_18 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.paragraph_1 {
  width: 340px;
  height: 120px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 20px;
  margin-top: 15px;
}

.box_3 {
  box-shadow: 0px 0px 8px 0px rgba(86, 86, 86, 0.5);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 380px;
  height: 415px;
  margin-left: 16px;
}

.image-text_6 {
  width: 340px;
  height: 358px;
  margin: 16px 0 0 20px;
}

.group_3 {
  background-image: url(./img/a93624873787473ab557840d124464f1_mergeImage.png);
  width: 340px;
  height: 180px;
}

.text-group_7 {
  width: 340px;
  height: 160px;
  margin-top: 18px;
}

.text_19 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_20 {
  width: 340px;
  height: 120px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 20px;
  margin-top: 15px;
}

.box_4 {
  box-shadow: 0px 0px 8px 0px rgba(86, 86, 86, 0.5);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 380px;
  height: 415px;
  margin-left: 10px;
}

.image-text_7 {
  width: 340px;
  height: 278px;
  margin: 16px 0 0 20px;
}

.section_5 {
  background-image: url(./img/8d72b24f0aff4fd98776998cfe0489a4_mergeImage.png);
  width: 340px;
  height: 180px;
}

.text-group_8 {
  width: 340px;
  height: 80px;
  margin-top: 18px;
}

.text_21 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_22 {
  width: 340px;
  height: 40px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 20px;
  margin-top: 15px;
}

.section_8 {
  width: 83px;
  height: 20px;
  margin: 749px 0 0 530px;
}

.text_23 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_2 {
  width: 7px;
  height: 14px;
  margin-top: 4px;
}

.image-wrapper_1 {
  border-radius: 6px;
  background-image: url(./img/90b2806365c44ec4ae6a3708ca84c279_mergeImage.png);
  height: 500px;
  width: 860px;
  margin: 13px 0 0 530px;
}

.image_2 {
  width: 100px;
  height: 100px;
  margin: 200px 0 0 380px;
}

.group_5 {
  height: 230px;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  position: relative;
  margin: 100px 0 1px 441px;
}

.text-wrapper_13 {
  width: 409px;
  height: 22px;
  margin: 18px 0 0 60px;
}

.text_24 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_25 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_26 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text-wrapper_14 {
  width: 443px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_27 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_28 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_29 {
  width: 95px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 112px;
}

.text-wrapper_15 {
  width: 63px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_30 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text-wrapper_16 {
  width: 32px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_31 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_12 {
  width: 178px;
  height: 55px;
  margin: 30px 0 3px 20px;
}

.image-text_8 {
  width: 178px;
  height: 55px;
}

.image_3 {
  width: 56px;
  height: 55px;
}

.text-group_4 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin-top: 3px;
}

.group_7 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 236px;
  top: 170px;
  width: 1241px;
  height: 60px;
}

.image_4 {
  position: absolute;
  left: 783px;
  top: 1421px;
  width: 354px;
  height: 4px;
}

.group_8 {
  position: absolute;
  left: -2px;
  top: 735px;
  width: 1920px;
  height: 600px;
  background: url(./img/SketchPng51cb4f1a65f9f9f2a80566ab0b76c9f8a77b108733ff7084a80bc3c141a63786.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.group_9 {
  background-image: url(./img/89c9827e71bd47e995825799d1464712_mergeImage.png);
  position: absolute;
  left: -2px;
  top: 2004px;
  width: 1920px;
  height: 630px;
}

.group_10 {
  box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.25);
  position: absolute;
  left: 445px;
  top: 263px;
  width: 1030px;
  height: 2279px;
}

.box_5 {
  border-radius: 8px;
  background-image: url(./img/b8d447e677534c7b957e2e9fc11992af_mergeImage.png);
  width: 1030px;
  height: 412px;
}

.text-group_9 {
  width: 540px;
  height: 291px;
  margin: 47px 0 0 437px;
}

.text_32 {
  width: 178px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 50px;
}

.paragraph_2 {
  width: 540px;
  height: 220px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin-top: 21px;
}

.text_33 {
  width: 71px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 50px;
  margin: 124px 0 0 -1px;
}

.box_6 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 400px;
  width: 1030px;
  margin: 23px 0 0 -2px;
}

.block_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 1030px;
  height: 400px;
}

.text-group_10 {
  width: 540px;
  height: 269px;
  margin: 37px 0 0 40px;
}

.text_34 {
  width: 390px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 50px;
}

.paragraph_3 {
  width: 540px;
  height: 198px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin-top: 21px;
}

.image_5 {
  width: 360px;
  height: 360px;
  margin: 23px 53px 0 37px;
}

.text_35 {
  width: 353px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 50px;
  margin: 787px 0 0 337px;
}

.text-wrapper_10 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 410px;
  width: 920px;
  margin: 23px 0 0 53px;
}

.paragraph_4 {
  width: 840px;
  height: 352px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 22px;
  margin: 27px 0 0 40px;
}

.image_6 {
  position: absolute;
  left: -2px;
  top: 535px;
  width: 48px;
  height: 4px;
}

.image_7 {
  position: absolute;
  left: 742px;
  top: 216px;
  width: 436px;
  height: 4px;
}
