html {
  font-size: 37.5px;
}

.page {
  position: relative;
  width: 51.2rem;
  height: 71.254rem;
  overflow: hidden;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 51.2rem;
  height: 71.254rem;
}

.group_18 {
  width: 43.547rem;
  height: 2.454rem;
}

.text_1 {
  width: 1.84rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin-top: 1.44rem;
}

.box_1 {
  border-radius: 50%;
  background-image: url(./img/79b144a48e324afe8d417c938894a984_mergeImage.png);
  width: 1.12rem;
  height: 1.12rem;
  border: 1px solid rgba(151, 151, 151, 1);
  margin: 0.614rem 0 0 2.96rem;
}

.text_2 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin: 0.56rem 0 0 0.32rem;
}

.box_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 33.067rem;
  height: 2.454rem;
  margin-left: 1.094rem;
}

.text_3 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 7.094rem;
}

.text_4 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.04rem;
}

.text_5 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_6 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_7 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.thumbnail_1 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 1.04rem 11.894rem 0 1.067rem;
}

.group_19 {
  width: 44.32rem;
  height: 2.454rem;
  margin-left: 6.88rem;
}

.text-wrapper_1 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(0, 85, 195, 1);
  height: 2.454rem;
  width: 3.147rem;
}

.text_8 {
  width: 1.44rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.box_3 {
  background-color: rgba(9, 9, 9, 1);
  position: relative;
  width: 38.934rem;
  height: 2.454rem;
}

.text_9 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 0.854rem;
}

.text_10 {
  width: 1.44rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.334rem;
}

.text_11 {
  width: 0.96rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.334rem;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  height: 2.454rem;
  width: 3.254rem;
  margin: 0 11.92rem 0 15.947rem;
}

.text_12 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.827rem 0 0 0.694rem;
}

.image_1 {
  position: absolute;
  left: -2.24rem;
  top: 1.2rem;
  width: 2.507rem;
  height: 0.027rem;
}

.group_3 {
  background-image: url(./img/aedda77a45764f4a9a84182ecfa9e1d7_mergeImage.png);
  width: 38.4rem;
  height: 10.187rem;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-left: 6.4rem;
}

.text_13 {
  width: 22.347rem;
  height: 1.734rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.28rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.734rem;
  margin: 2.454rem 0 0 7.787rem;
}

.text-wrapper_3 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.227rem;
  width: 2.667rem;
  margin: 1.467rem 0 0 17.867rem;
}

.text_14 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.image_2 {
  width: 3.414rem;
  height: 0.374rem;
  margin: 2.427rem 0 0.507rem 17.494rem;
}

.group_4 {
  background-image: url(./img/744457cc39614e1b9eb6e9397ab27d32_mergeImage.png);
  height: 18.134rem;
  margin-top: 1.6rem;
  width: 51.2rem;
}

.text-wrapper_25 {
  width: 3.174rem;
  height: 1.12rem;
  margin: 1.52rem 0 0 24.027rem;
}

.text_15 {
  width: 3.174rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text-wrapper_26 {
  width: 18.187rem;
  height: 0.534rem;
  margin: 0.854rem 0 0 16.507rem;
}

.text_16 {
  width: 2.96rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_17 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin-left: 2.134rem;
}

.text_18 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin-left: 2.134rem;
}

.text_19 {
  width: 2.214rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin-left: 2.134rem;
}

.text_20 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin-left: 2.134rem;
}

.box_9 {
  width: 2.8rem;
  height: 2.72rem;
  margin: 1.04rem 0 0 37.2rem;
}

.group_5 {
  box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
  border-radius: 8px;
  background-image: url(./img/d95c1a527e584dbd8ba4a677be6c22d9_mergeImage.png);
  width: 2.8rem;
  height: 2.72rem;
}

.box_10 {
  width: 2.8rem;
  height: 2.72rem;
  margin: 0.32rem 0 0 37.2rem;
}

.group_6 {
  box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
  border-radius: 8px;
  background-image: url(./img/d84667fe1ad94a8290dd55bdfb3ae3be_mergeImage.png);
  width: 2.8rem;
  height: 2.72rem;
}

.box_11 {
  width: 28.294rem;
  height: 2.72rem;
  margin: 0.32rem 0 0 11.707rem;
}

.text_21 {
  width: 1.84rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin-top: 2rem;
}

.box_4 {
  box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
  border-radius: 8px;
  background-image: url(./img/5fa0fd3476574832a0fa7dc2e79e889a_mergeImage.png);
  width: 2.8rem;
  height: 2.72rem;
}

.box_12 {
  width: 28.374rem;
  height: 2.854rem;
  margin: 0.054rem 0 1.36rem 11.627rem;
}

.text-wrapper_6 {
  background-color: rgba(0, 0, 0, 1);
  height: 0.907rem;
  width: 2.08rem;
}

.text_22 {
  width: 1.12rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.16rem 0 0 0.48rem;
}

.image_3 {
  width: 1.894rem;
  height: 1.414rem;
  margin: 0.667rem 0 0 0.427rem;
}

.text-wrapper_7 {
  background-color: rgba(0, 0, 0, 0);
  height: 0.907rem;
  border: 1px solid rgba(0, 0, 0, 1);
  width: 2.08rem;
  margin: 1.707rem 0 0 -0.027rem;
}

.text_23 {
  width: 1.12rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.16rem 0 0 0.48rem;
}

.text-wrapper_8 {
  background-color: rgba(0, 0, 0, 0);
  height: 0.907rem;
  border: 1px solid rgba(0, 0, 0, 1);
  width: 2.08rem;
  margin: 1.707rem 0 0 1.067rem;
}

.text_24 {
  width: 1.12rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.16rem 0 0 0.48rem;
}

.text-group_16 {
  width: 1.707rem;
  height: 1.28rem;
  margin: 1.574rem 0 0 6.72rem;
}

.text_25 {
  width: 1.707rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.56rem;
}

.text_26 {
  width: 0.64rem;
  height: 0.454rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.32rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.454rem;
  margin-top: 0.24rem;
}

.group_7 {
  width: 0.027rem;
  height: 1.254rem;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 1.6rem 0 0 0.8rem;
}

.text-group_17 {
  width: 1.574rem;
  height: 1.28rem;
  margin: 1.574rem 0 0 0.8rem;
}

.text_27 {
  width: 1.12rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.56rem;
}

.text_28 {
  width: 1.574rem;
  height: 0.454rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.32rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.454rem;
  margin-top: 0.267rem;
}

.group_8 {
  width: 0.027rem;
  height: 1.254rem;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 1.6rem 0 0 0.827rem;
}

.text-group_18 {
  width: 1.654rem;
  height: 1.307rem;
  margin: 1.547rem 0 0 0.8rem;
}

.text-wrapper_9 {
  width: 1.654rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
}

.text_29 {
  width: 1.654rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
}

.text_30 {
  width: 1.654rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
}

.text_31 {
  width: 0.64rem;
  height: 0.454rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.32rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.454rem;
  margin-top: 0.187rem;
}

.group_9 {
  width: 0.027rem;
  height: 1.254rem;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 1.6rem 0 0 0.8rem;
}

.text-group_19 {
  width: 2.214rem;
  height: 1.307rem;
  margin: 1.547rem 0 0 0.8rem;
}

.text-wrapper_10 {
  width: 2.214rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
}

.text_32 {
  width: 2.214rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
}

.text_33 {
  width: 2.214rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
}

.text_34 {
  width: 0.64rem;
  height: 0.454rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.32rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.454rem;
  margin-top: 0.187rem;
}

.text-group_20 {
  width: 23.227rem;
  height: 3.174rem;
  margin: 1.52rem 0 0 13.68rem;
}

.text_35 {
  width: 9.467rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.334rem;
  margin-left: 7.2rem;
}

.text_36 {
  width: 23.227rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  line-height: 0.72rem;
  margin-top: 0.4rem;
}

.group_10 {
  background-color: rgba(0, 0, 0, 1);
  width: 27.467rem;
  height: 3.2rem;
  margin: 1.494rem 0 0 11.547rem;
}

.text_37 {
  width: 2.56rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.64rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 1.147rem 0 0 5.494rem;
}

.text-wrapper_11 {
  background-image: url(./img/11c09b6d4d974cfdb0174ba7f2c9e083_mergeImage.png);
  height: 3.2rem;
  margin-left: 5.787rem;
  width: 13.627rem;
}

.text_38 {
  width: 2.56rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.64rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 1.147rem 0 0 5.547rem;
}

.group_20 {
  width: 30.4rem;
  height: 1.334rem;
  margin: 1.52rem 0 0 13.6rem;
}

.text_39 {
  width: 3.787rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.334rem;
}

.image-text_8 {
  width: 2.134rem;
  height: 0.534rem;
  margin: 0.587rem 0 0 19.334rem;
}

.text-group_6 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(118, 118, 118, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_2 {
  width: 0.374rem;
  height: 0.374rem;
  margin-top: 0.107rem;
}

.image_4 {
  width: 2.054rem;
  height: 0.027rem;
  margin: 0.854rem 0 0 0.534rem;
}

.image-text_9 {
  width: 2.134rem;
  height: 0.534rem;
  margin: 0.587rem 0 0 0.427rem;
}

.text-group_7 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 58, 133, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_3 {
  width: 0.374rem;
  height: 0.374rem;
  margin-top: 0.107rem;
}

.list_3 {
  width: 27.467rem;
  height: 9.014rem;
  justify-content: space-between;
  margin: 0.614rem 0 0 11.547rem;
}

.image-text_3-0 {
  background-color: rgba(244, 244, 244, 1);
  width: 6.667rem;
  height: 9.014rem;
  margin-right: 0.267rem;
}

.group_12-0 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 6.667rem;
  height: 3.414rem;
  background: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
}

.text-group_21-0 {
  width: 5.547rem;
  height: 2.8rem;
  margin: 0.267rem 0 2.534rem 0.534rem;
}

.text_40-0 {
  width: 5.547rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.72rem;
}

.text_41-0 {
  width: 5.547rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.587rem;
  margin-top: 0.187rem;
}

.image-text_3-1 {
  background-color: rgba(244, 244, 244, 1);
  width: 6.667rem;
  height: 9.014rem;
  margin-right: 0.267rem;
}

.group_12-1 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 6.667rem;
  height: 3.414rem;
  background: url(./img/6e327735808b4c35be47442543b0094a_mergeImage.png);
}

.text-group_21-1 {
  width: 5.547rem;
  height: 2.8rem;
  margin: 0.267rem 0 2.534rem 0.534rem;
}

.text_40-1 {
  width: 5.547rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.72rem;
}

.text_41-1 {
  width: 5.547rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.587rem;
  margin-top: 0.187rem;
}

.image-text_3-2 {
  background-color: rgba(244, 244, 244, 1);
  width: 6.667rem;
  height: 9.014rem;
  margin-right: 0.267rem;
}

.group_12-2 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 6.667rem;
  height: 3.414rem;
  background: url(./img/050735b55ad74444aa753f87269e74f6_mergeImage.png);
}

.text-group_21-2 {
  width: 5.547rem;
  height: 2.8rem;
  margin: 0.267rem 0 2.534rem 0.534rem;
}

.text_40-2 {
  width: 5.547rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.72rem;
}

.text_41-2 {
  width: 5.547rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.587rem;
  margin-top: 0.187rem;
}

.image-text_3-3 {
  background-color: rgba(244, 244, 244, 1);
  width: 6.667rem;
  height: 9.014rem;
  margin-right: 0.267rem;
}

.group_12-3 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 6.667rem;
  height: 3.414rem;
  background: url(./img/a40c4aded9fe41128fc2d24e4588ad00_mergeImage.png);
}

.text-group_21-3 {
  width: 5.547rem;
  height: 2.8rem;
  margin: 0.267rem 0 2.534rem 0.534rem;
}

.text_40-3 {
  width: 5.547rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.533rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.72rem;
}

.text_41-3 {
  width: 5.547rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.587rem;
  margin-top: 0.187rem;
}

.group_13 {
  height: 4.534rem;
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.467rem;
  margin: 1.6rem 0 0 11.547rem;
}

.text-wrapper_27 {
  width: 2.534rem;
  height: 0.88rem;
  margin: 0.694rem 0 0 1.334rem;
}

.text_42 {
  width: 2.534rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.64rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.group_21 {
  width: 22.907rem;
  height: 1.28rem;
  margin: 0.08rem 0 1.6rem 1.334rem;
}

.text_43 {
  width: 17.04rem;
  height: 1.067rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
  margin-top: 0.214rem;
}

.text-wrapper_13 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.227rem;
  width: 2.667rem;
}

.text_44 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.group_15 {
  height: 6.134rem;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.467rem;
  position: relative;
  margin: 2.294rem 0 0 11.547rem;
}

.text-wrapper_28 {
  width: 10.907rem;
  height: 0.587rem;
  margin: 0.48rem 0 0 1.6rem;
}

.text_45 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_46 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_47 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text-wrapper_29 {
  width: 11.814rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_48 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_49 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_50 {
  width: 2.534rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.987rem;
}

.text-wrapper_30 {
  width: 1.68rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_51 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text-wrapper_31 {
  width: 0.854rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_52 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.group_22 {
  width: 4.747rem;
  height: 1.467rem;
  margin: 0.8rem 0 0.08rem 0.534rem;
}

.image-text_10 {
  width: 4.747rem;
  height: 1.467rem;
}

.image_5 {
  width: 1.494rem;
  height: 1.467rem;
}

.text-group_9 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin-top: 0.08rem;
}

.group_17 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 6.56rem;
  top: 4.534rem;
  width: 33.094rem;
  height: 1.6rem;
}
